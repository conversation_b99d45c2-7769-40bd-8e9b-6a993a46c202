# Qwen-2.5-VL 文档解析评测完整指南

## 第一步：环境配置

### 1.1 创建虚拟环境
```bash
# 创建并激活虚拟环境
conda create -n qwen_omnidocbench python=3.10 -y
conda activate qwen_omnidocbench

# 或者使用脚本自动配置
chmod +x setup_qwen_environment.sh
./setup_qwen_environment.sh
```

### 1.2 安装依赖
```bash
# 基础依赖
pip install -r requirements.txt

# Qwen-VL 相关依赖
pip install transformers>=4.37.0
pip install torch>=2.0.0 torchvision
pip install qwen-vl-utils
pip install accelerate
pip install flash-attn --no-build-isolation

# 评测相关依赖
pip install evaluate python-Levenshtein scipy
```

### 1.3 下载模型
```bash
# 方法1：使用 HuggingFace Hub
from transformers import Qwen2VLForConditionalGeneration
model = Qwen2VLForConditionalGeneration.from_pretrained("Qwen/Qwen2.5-VL-7B-Instruct")

# 方法2：使用 ModelScope
from modelscope import snapshot_download
model_dir = snapshot_download('qwen/Qwen2.5-VL-7B-Instruct')

# 方法3：手动下载到本地目录
# 将模型文件放置在指定目录，如：./models/Qwen2.5-VL-7B-Instruct/
```

## 第二步：数据准备

### 2.1 使用示例数据（快速开始）
```bash
# 项目已包含示例数据
ls demo_data/omnidocbench_demo/images/     # 输入图片
ls demo_data/omnidocbench_demo/mds/        # 标准答案
```

### 2.2 准备自定义数据
```bash
# 使用数据准备工具
python tools/prepare_custom_dataset.py \
    --source_dir /path/to/your/images \
    --target_dir /path/to/custom_dataset

# 如果有 PDF 文件需要转换
python tools/prepare_custom_dataset.py \
    --source_dir /path/to/your/pdfs \
    --target_dir /path/to/custom_dataset \
    --convert_pdf \
    --pdf_dpi 300
```

### 2.3 数据格式验证
```bash
# 检查数据格式
python -c "
import os
images = os.listdir('your_dataset/images')
mds = os.listdir('your_dataset/ground_truth/mds')
print(f'图片文件: {len(images)}')
print(f'标准答案: {len(mds)}')
print('文件名匹配检查...')
for img in images:
    base_name = os.path.splitext(img)[0]
    if f'{base_name}.md' not in mds:
        print(f'缺少对应的 MD 文件: {img}')
"
```

## 第三步：模型推理

### 3.1 单独执行推理
```bash
# 使用自定义推理脚本
python tools/model_infer/Qwen25VL_img2md_custom.py \
    --model_path /path/to/qwen2.5-vl-model \
    --input_dir ./demo_data/omnidocbench_demo/images \
    --output_dir ./result/qwen25vl_predictions \
    --max_new_tokens 32000 \
    --temperature 0.01
```

### 3.2 推理参数调优
```bash
# 高质量推理（慢但准确）
python tools/model_infer/Qwen25VL_img2md_custom.py \
    --model_path /path/to/model \
    --input_dir ./images \
    --output_dir ./predictions \
    --max_new_tokens 40000 \
    --temperature 0.0

# 快速推理（快但可能不够准确）
python tools/model_infer/Qwen25VL_img2md_custom.py \
    --model_path /path/to/model \
    --input_dir ./images \
    --output_dir ./predictions \
    --max_new_tokens 16000 \
    --temperature 0.1
```

## 第四步：执行评测

### 4.1 配置评测参数
编辑 `configs/md2md.yaml`：
```yaml
end2end_eval:
  dataset:
    ground_truth:
      data_path: ./demo_data/omnidocbench_demo/mds
      page_info: ./demo_data/omnidocbench_demo/OmniDocBench_demo.json
    prediction:
      data_path: ./result/qwen25vl_predictions
    match_method: quick_match  # 推荐使用
    # 可选：页面筛选
    # filter:
    #   language: english
```

### 4.2 执行评测
```bash
# 标准评测
python pdf_validation.py --config configs/md2md.yaml

# 或使用完整流程脚本
python run_qwen25vl_evaluation.py \
    --model_path /path/to/qwen2.5-vl-model \
    --input_images ./demo_data/omnidocbench_demo/images \
    --ground_truth_mds ./demo_data/omnidocbench_demo/mds \
    --ground_truth_json ./demo_data/omnidocbench_demo/OmniDocBench_demo.json \
    --output_dir ./result/qwen25vl_predictions
```

### 4.3 仅执行评测（跳过推理）
```bash
# 如果已有预测结果，可跳过推理步骤
python run_qwen25vl_evaluation.py \
    --model_path /path/to/model \
    --input_images ./images \
    --ground_truth_mds ./ground_truth/mds \
    --ground_truth_json ./ground_truth/dataset.json \
    --output_dir ./existing_predictions \
    --skip_inference
```

## 第五步：结果分析

### 5.1 查看评测结果
```bash
# 结果文件位置
ls result/
# qwen25vl_predictions_quick_match_metric_result.json  # 主要指标
# qwen25vl_predictions_quick_match_text_block_result.json  # 文本详细结果
# qwen25vl_predictions_quick_match_display_formula_result.json  # 公式详细结果
# qwen25vl_predictions_quick_match_table_result.json  # 表格详细结果
```

### 5.2 结果解读
```python
import json

# 读取主要指标
with open('result/qwen25vl_predictions_quick_match_metric_result.json', 'r') as f:
    results = json.load(f)

# 打印主要指标
for category, metrics in results.items():
    print(f"\n=== {category} ===")
    for metric_name, values in metrics.items():
        if isinstance(values, dict):
            for group, score in values.items():
                print(f"{metric_name} ({group}): {score:.4f}")
```

### 5.3 生成评测报告
```bash
# 使用 Jupyter Notebook 生成详细报告
jupyter notebook tools/generate_result_tables.ipynb
```

## 第六步：性能优化建议

### 6.1 模型选择
- **Qwen2.5-VL-7B**: 平衡性能和速度，适合大多数场景
- **Qwen2.5-VL-72B**: 最高精度，需要更多计算资源

### 6.2 推理优化
```python
# 使用 Flash Attention 2
attn_implementation="flash_attention_2"

# 使用 BF16 精度
torch_dtype=torch.bfloat16

# 多 GPU 推理
device_map="auto"
```

### 6.3 评测策略选择
- **quick_match**: 推荐，处理段落分割差异
- **simple_match**: 快速评测，适合格式规范的数据
- **no_split**: 整体评测，适合短文档

## 常见问题解决

### Q1: CUDA 内存不足
```bash
# 解决方案1：使用更小的模型
model_path = "Qwen/Qwen2.5-VL-7B-Instruct"  # 而不是 72B

# 解决方案2：减少 max_new_tokens
--max_new_tokens 16000

# 解决方案3：使用 CPU 推理（慢）
device_map="cpu"
```

### Q2: 推理速度慢
```bash
# 解决方案1：使用 Flash Attention
pip install flash-attn --no-build-isolation

# 解决方案2：批量处理
# 修改推理脚本支持批量输入

# 解决方案3：使用量化模型
# 加载 4bit 或 8bit 量化版本
```

### Q3: 评测结果异常
```bash
# 检查文件编码
file -bi your_file.md

# 检查文件格式
python -c "
with open('file.md', 'r', encoding='utf-8') as f:
    content = f.read()
    print('LaTeX formulas:', content.count('\\['))
    print('HTML tables:', content.count('<table>'))
"
```
