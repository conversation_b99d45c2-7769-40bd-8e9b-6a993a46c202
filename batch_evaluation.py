#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量评测脚本 - 支持多个模型、多个数据集的批量评测
"""

import os
import json
import argparse
import subprocess
import logging
from pathlib import Path
from datetime import datetime
import pandas as pd

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchEvaluator:
    def __init__(self, config_file):
        """
        初始化批量评测器
        
        Args:
            config_file: 批量评测配置文件路径
        """
        self.config_file = config_file
        self.load_config()
        self.results = []
        
    def load_config(self):
        """加载批量评测配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        logger.info(f"加载配置文件: {self.config_file}")
        logger.info(f"模型数量: {len(self.config['models'])}")
        logger.info(f"数据集数量: {len(self.config['datasets'])}")
    
    def run_single_evaluation(self, model_config, dataset_config):
        """
        执行单个模型在单个数据集上的评测
        
        Args:
            model_config: 模型配置
            dataset_config: 数据集配置
            
        Returns:
            评测结果字典
        """
        model_name = model_config['name']
        dataset_name = dataset_config['name']
        
        logger.info(f"开始评测: {model_name} on {dataset_name}")
        
        # 构建输出目录
        output_dir = Path(self.config['output_base_dir']) / f"{model_name}_{dataset_name}"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 1. 执行推理
            if not self.config.get('skip_inference', False):
                self.run_inference(model_config, dataset_config, output_dir)
            
            # 2. 执行评测
            results = self.run_evaluation(model_config, dataset_config, output_dir)
            
            # 3. 记录结果
            result_record = {
                'model_name': model_name,
                'dataset_name': dataset_name,
                'timestamp': datetime.now().isoformat(),
                'status': 'success',
                'results': results,
                'output_dir': str(output_dir)
            }
            
            logger.info(f"评测完成: {model_name} on {dataset_name}")
            return result_record
            
        except Exception as e:
            logger.error(f"评测失败: {model_name} on {dataset_name} - {e}")
            return {
                'model_name': model_name,
                'dataset_name': dataset_name,
                'timestamp': datetime.now().isoformat(),
                'status': 'failed',
                'error': str(e),
                'output_dir': str(output_dir)
            }
    
    def run_inference(self, model_config, dataset_config, output_dir):
        """执行模型推理"""
        logger.info("执行模型推理...")
        
        cmd = [
            "python", "tools/model_infer/Qwen25VL_img2md_custom.py",
            "--model_path", model_config['path'],
            "--input_dir", dataset_config['images_dir'],
            "--output_dir", str(output_dir / "predictions"),
            "--max_new_tokens", str(model_config.get('max_new_tokens', 32000)),
            "--temperature", str(model_config.get('temperature', 0.01))
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("推理完成")
    
    def run_evaluation(self, model_config, dataset_config, output_dir):
        """执行评测"""
        logger.info("执行评测...")
        
        # 创建临时配置文件
        temp_config = self.create_temp_config(dataset_config, output_dir)
        
        cmd = [
            "python", "pdf_validation.py",
            "--config", str(temp_config)
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # 读取评测结果
        result_files = list(Path("result").glob(f"*{output_dir.name}*metric_result.json"))
        if result_files:
            with open(result_files[0], 'r', encoding='utf-8') as f:
                results = json.load(f)
            return results
        else:
            logger.warning("未找到评测结果文件")
            return {}
    
    def create_temp_config(self, dataset_config, output_dir):
        """创建临时评测配置文件"""
        temp_config = {
            "end2end_eval": {
                "metrics": {
                    "text_block": {"metric": ["Edit_dist", "METEOR", "BLEU"]},
                    "display_formula": {"metric": ["Edit_dist", "CDM"]},
                    "table": {"metric": ["TEDS", "Edit_dist"]},
                    "reading_order": {"metric": ["Edit_dist"]}
                },
                "dataset": {
                    "dataset_name": "md2md_dataset",
                    "ground_truth": {
                        "data_path": dataset_config['ground_truth_mds'],
                        "page_info": dataset_config.get('ground_truth_json', "")
                    },
                    "prediction": {
                        "data_path": str(output_dir / "predictions")
                    },
                    "match_method": dataset_config.get('match_method', 'quick_match')
                }
            }
        }
        
        # 添加筛选条件
        if 'filter' in dataset_config:
            temp_config["end2end_eval"]["dataset"]["filter"] = dataset_config['filter']
        
        # 保存临时配置文件
        temp_config_path = output_dir / "temp_config.yaml"
        import yaml
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(temp_config, f, default_flow_style=False, allow_unicode=True)
        
        return temp_config_path
    
    def run_batch_evaluation(self):
        """执行批量评测"""
        logger.info("开始批量评测...")
        
        total_tasks = len(self.config['models']) * len(self.config['datasets'])
        current_task = 0
        
        for model_config in self.config['models']:
            for dataset_config in self.config['datasets']:
                current_task += 1
                logger.info(f"进度: {current_task}/{total_tasks}")
                
                result = self.run_single_evaluation(model_config, dataset_config)
                self.results.append(result)
                
                # 保存中间结果
                self.save_intermediate_results()
        
        # 生成最终报告
        self.generate_final_report()
        
        logger.info("批量评测完成！")
    
    def save_intermediate_results(self):
        """保存中间结果"""
        results_file = Path(self.config['output_base_dir']) / "batch_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
    
    def generate_final_report(self):
        """生成最终评测报告"""
        logger.info("生成最终评测报告...")
        
        # 创建汇总表格
        summary_data = []
        
        for result in self.results:
            if result['status'] == 'success' and 'results' in result:
                for category, metrics in result['results'].items():
                    for metric_name, values in metrics.items():
                        if isinstance(values, dict):
                            for group, score in values.items():
                                summary_data.append({
                                    'Model': result['model_name'],
                                    'Dataset': result['dataset_name'],
                                    'Category': category,
                                    'Metric': metric_name,
                                    'Group': group,
                                    'Score': score
                                })
                        else:
                            summary_data.append({
                                'Model': result['model_name'],
                                'Dataset': result['dataset_name'],
                                'Category': category,
                                'Metric': metric_name,
                                'Group': 'overall',
                                'Score': values
                            })
        
        # 保存为 CSV
        if summary_data:
            df = pd.DataFrame(summary_data)
            csv_file = Path(self.config['output_base_dir']) / "evaluation_summary.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8')
            logger.info(f"评测汇总表保存至: {csv_file}")
            
            # 生成透视表
            pivot_table = df.pivot_table(
                values='Score', 
                index=['Model', 'Dataset'], 
                columns=['Category', 'Metric'], 
                aggfunc='mean'
            )
            pivot_file = Path(self.config['output_base_dir']) / "evaluation_pivot.csv"
            pivot_table.to_csv(pivot_file, encoding='utf-8')
            logger.info(f"透视表保存至: {pivot_file}")
        
        # 生成 HTML 报告
        self.generate_html_report()
    
    def generate_html_report(self):
        """生成 HTML 格式的评测报告"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>批量评测报告</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .success {{ color: green; }}
        .failed {{ color: red; }}
    </style>
</head>
<body>
    <h1>批量评测报告</h1>
    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <h2>评测概览</h2>
    <table>
        <tr>
            <th>模型</th>
            <th>数据集</th>
            <th>状态</th>
            <th>时间</th>
        </tr>
"""
        
        for result in self.results:
            status_class = "success" if result['status'] == 'success' else "failed"
            html_content += f"""
        <tr>
            <td>{result['model_name']}</td>
            <td>{result['dataset_name']}</td>
            <td class="{status_class}">{result['status']}</td>
            <td>{result['timestamp']}</td>
        </tr>
"""
        
        html_content += """
    </table>
    
    <h2>详细结果</h2>
    <p>详细的评测结果请查看对应的 JSON 文件和 CSV 汇总表。</p>
    
</body>
</html>
"""
        
        html_file = Path(self.config['output_base_dir']) / "evaluation_report.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML 报告保存至: {html_file}")

def create_sample_config():
    """创建示例配置文件"""
    sample_config = {
        "output_base_dir": "./batch_evaluation_results",
        "skip_inference": False,
        "models": [
            {
                "name": "Qwen2.5-VL-7B",
                "path": "/path/to/Qwen2.5-VL-7B-Instruct",
                "max_new_tokens": 32000,
                "temperature": 0.01
            },
            {
                "name": "Qwen2.5-VL-72B",
                "path": "/path/to/Qwen2.5-VL-72B-Instruct",
                "max_new_tokens": 40000,
                "temperature": 0.0
            }
        ],
        "datasets": [
            {
                "name": "demo_dataset",
                "images_dir": "./demo_data/omnidocbench_demo/images",
                "ground_truth_mds": "./demo_data/omnidocbench_demo/mds",
                "ground_truth_json": "./demo_data/omnidocbench_demo/OmniDocBench_demo.json",
                "match_method": "quick_match"
            },
            {
                "name": "custom_dataset",
                "images_dir": "./custom_data/images",
                "ground_truth_mds": "./custom_data/ground_truth/mds",
                "ground_truth_json": "./custom_data/ground_truth/dataset.json",
                "match_method": "quick_match",
                "filter": {
                    "language": "english"
                }
            }
        ]
    }
    
    with open("batch_config_sample.json", 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
    
    print("示例配置文件已创建: batch_config_sample.json")

def main():
    parser = argparse.ArgumentParser(description='批量评测脚本')
    parser.add_argument('--config', type=str, required=True,
                       help='批量评测配置文件')
    parser.add_argument('--create_sample', action='store_true',
                       help='创建示例配置文件')
    
    args = parser.parse_args()
    
    if args.create_sample:
        create_sample_config()
        return
    
    # 执行批量评测
    evaluator = BatchEvaluator(args.config)
    evaluator.run_batch_evaluation()

if __name__ == "__main__":
    main()
