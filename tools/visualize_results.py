#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评测结果可视化工具
生成图表和报告，帮助分析模型性能
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
import argparse
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResultVisualizer:
    def __init__(self, result_dir):
        self.result_dir = Path(result_dir)
        self.results_data = {}
        self.load_results()
        
    def load_results(self):
        """加载评测结果文件"""
        logger.info(f"从 {self.result_dir} 加载结果文件...")
        
        # 查找所有结果文件
        result_files = list(self.result_dir.glob("*metric_result.json"))
        
        if not result_files:
            logger.error("未找到评测结果文件")
            return
        
        for result_file in result_files:
            # 从文件名提取模型名称
            model_name = result_file.stem.replace("_metric_result", "")
            
            with open(result_file, 'r', encoding='utf-8') as f:
                self.results_data[model_name] = json.load(f)
            
            logger.info(f"加载结果: {model_name}")
    
    def create_performance_overview(self):
        """创建性能概览图表"""
        logger.info("生成性能概览图表...")
        
        # 准备数据
        overview_data = []
        
        for model_name, results in self.results_data.items():
            for category, metrics in results.items():
                for metric_name, values in metrics.items():
                    if isinstance(values, dict):
                        # 使用 overall 分数，如果没有则使用平均值
                        if 'overall' in values:
                            score = values['overall']
                        else:
                            # 计算所有分组的平均值
                            numeric_values = [v for v in values.values() if isinstance(v, (int, float))]
                            score = np.mean(numeric_values) if numeric_values else 0
                    else:
                        score = values
                    
                    overview_data.append({
                        'Model': model_name,
                        'Category': category,
                        'Metric': metric_name,
                        'Score': score
                    })
        
        if not overview_data:
            logger.warning("没有可用的概览数据")
            return
        
        df = pd.DataFrame(overview_data)
        
        # 创建子图
        categories = df['Category'].unique()
        n_categories = len(categories)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        for i, category in enumerate(categories):
            if i >= 4:  # 最多显示4个类别
                break
                
            category_data = df[df['Category'] == category]
            
            # 创建热力图数据
            pivot_data = category_data.pivot(index='Model', columns='Metric', values='Score')
            
            # 绘制热力图
            sns.heatmap(pivot_data, annot=True, fmt='.3f', cmap='RdYlGn', 
                       ax=axes[i], cbar_kws={'label': 'Score'})
            axes[i].set_title(f'{category} Performance')
            axes[i].set_xlabel('Metrics')
            axes[i].set_ylabel('Models')
        
        # 隐藏多余的子图
        for i in range(n_categories, 4):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(self.result_dir / 'performance_overview.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("性能概览图表已保存: performance_overview.png")
    
    def create_metric_comparison(self):
        """创建指标对比图表"""
        logger.info("生成指标对比图表...")
        
        # 准备数据
        comparison_data = []
        
        for model_name, results in self.results_data.items():
            model_scores = {'Model': model_name}
            
            # 提取主要指标
            if 'text_block' in results:
                if 'Edit_dist' in results['text_block']:
                    edit_dist = results['text_block']['Edit_dist']
                    if isinstance(edit_dist, dict) and 'overall' in edit_dist:
                        model_scores['Text_Edit_Dist'] = edit_dist['overall']
                    elif isinstance(edit_dist, (int, float)):
                        model_scores['Text_Edit_Dist'] = edit_dist
                
                if 'BLEU' in results['text_block']:
                    bleu = results['text_block']['BLEU']
                    if isinstance(bleu, dict) and 'overall' in bleu:
                        model_scores['Text_BLEU'] = bleu['overall']
                    elif isinstance(bleu, (int, float)):
                        model_scores['Text_BLEU'] = bleu
            
            if 'display_formula' in results:
                if 'Edit_dist' in results['display_formula']:
                    formula_edit = results['display_formula']['Edit_dist']
                    if isinstance(formula_edit, dict) and 'overall' in formula_edit:
                        model_scores['Formula_Edit_Dist'] = formula_edit['overall']
                    elif isinstance(formula_edit, (int, float)):
                        model_scores['Formula_Edit_Dist'] = formula_edit
            
            if 'table' in results:
                if 'TEDS' in results['table']:
                    teds = results['table']['TEDS']
                    if isinstance(teds, dict) and 'overall' in teds:
                        model_scores['Table_TEDS'] = teds['overall']
                    elif isinstance(teds, (int, float)):
                        model_scores['Table_TEDS'] = teds
            
            comparison_data.append(model_scores)
        
        if not comparison_data:
            logger.warning("没有可用的对比数据")
            return
        
        df = pd.DataFrame(comparison_data)
        
        # 创建雷达图
        metrics = [col for col in df.columns if col != 'Model']
        n_metrics = len(metrics)
        
        if n_metrics == 0:
            logger.warning("没有找到有效的指标数据")
            return
        
        # 设置雷达图
        angles = np.linspace(0, 2 * np.pi, n_metrics, endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(df)))
        
        for i, (_, row) in enumerate(df.iterrows()):
            values = []
            for metric in metrics:
                value = row.get(metric, 0)
                # 对于编辑距离，转换为相似度分数 (1 - edit_distance)
                if 'Edit_Dist' in metric:
                    value = 1 - value if value <= 1 else 0
                values.append(value)
            
            values += values[:1]  # 闭合图形
            
            ax.plot(angles, values, 'o-', linewidth=2, label=row['Model'], color=colors[i])
            ax.fill(angles, values, alpha=0.25, color=colors[i])
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_title('Model Performance Comparison (Radar Chart)', size=16, pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)
        
        plt.tight_layout()
        plt.savefig(self.result_dir / 'metric_comparison_radar.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("指标对比雷达图已保存: metric_comparison_radar.png")
    
    def create_category_breakdown(self):
        """创建分类别性能分解图表"""
        logger.info("生成分类别性能分解图表...")
        
        for model_name, results in self.results_data.items():
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            axes = axes.flatten()
            
            plot_idx = 0
            
            for category, metrics in results.items():
                if plot_idx >= 4:
                    break
                
                # 准备该类别的数据
                category_data = []
                
                for metric_name, values in metrics.items():
                    if isinstance(values, dict):
                        for group, score in values.items():
                            if isinstance(score, (int, float)):
                                category_data.append({
                                    'Metric': metric_name,
                                    'Group': group,
                                    'Score': score
                                })
                
                if not category_data:
                    continue
                
                df_cat = pd.DataFrame(category_data)
                
                # 创建分组柱状图
                if len(df_cat) > 0:
                    pivot_cat = df_cat.pivot(index='Group', columns='Metric', values='Score')
                    pivot_cat.plot(kind='bar', ax=axes[plot_idx], rot=45)
                    axes[plot_idx].set_title(f'{category} - {model_name}')
                    axes[plot_idx].set_xlabel('Groups')
                    axes[plot_idx].set_ylabel('Score')
                    axes[plot_idx].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
                
                plot_idx += 1
            
            # 隐藏多余的子图
            for i in range(plot_idx, 4):
                axes[i].set_visible(False)
            
            plt.tight_layout()
            plt.savefig(self.result_dir / f'category_breakdown_{model_name}.png', 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"分类别分解图表已保存: category_breakdown_{model_name}.png")
    
    def generate_summary_report(self):
        """生成汇总报告"""
        logger.info("生成汇总报告...")
        
        report_content = "# 评测结果汇总报告\n\n"
        report_content += f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 模型概览
        report_content += "## 模型概览\n\n"
        for model_name in self.results_data.keys():
            report_content += f"- {model_name}\n"
        report_content += "\n"
        
        # 主要指标汇总
        report_content += "## 主要指标汇总\n\n"
        
        summary_table = []
        for model_name, results in self.results_data.items():
            row = {'Model': model_name}
            
            # 提取关键指标
            if 'text_block' in results and 'Edit_dist' in results['text_block']:
                edit_dist = results['text_block']['Edit_dist']
                if isinstance(edit_dist, dict) and 'overall' in edit_dist:
                    row['Text Edit Distance'] = f"{edit_dist['overall']:.4f}"
            
            if 'text_block' in results and 'BLEU' in results['text_block']:
                bleu = results['text_block']['BLEU']
                if isinstance(bleu, dict) and 'overall' in bleu:
                    row['Text BLEU'] = f"{bleu['overall']:.4f}"
            
            if 'display_formula' in results and 'Edit_dist' in results['display_formula']:
                formula_edit = results['display_formula']['Edit_dist']
                if isinstance(formula_edit, dict) and 'overall' in formula_edit:
                    row['Formula Edit Distance'] = f"{formula_edit['overall']:.4f}"
            
            if 'table' in results and 'TEDS' in results['table']:
                teds = results['table']['TEDS']
                if isinstance(teds, dict) and 'overall' in teds:
                    row['Table TEDS'] = f"{teds['overall']:.4f}"
            
            summary_table.append(row)
        
        if summary_table:
            df_summary = pd.DataFrame(summary_table)
            report_content += df_summary.to_markdown(index=False) + "\n\n"
        
        # 性能分析
        report_content += "## 性能分析\n\n"
        report_content += "### 文本识别性能\n"
        report_content += "- Edit Distance 越小越好（0.0 为完美）\n"
        report_content += "- BLEU 分数越大越好（1.0 为完美）\n\n"
        
        report_content += "### 公式识别性能\n"
        report_content += "- Edit Distance 越小越好\n"
        report_content += "- 公式识别通常比文本识别更具挑战性\n\n"
        
        report_content += "### 表格识别性能\n"
        report_content += "- TEDS 分数越大越好（1.0 为完美）\n"
        report_content += "- 考虑表格结构和内容的综合相似度\n\n"
        
        # 保存报告
        report_file = self.result_dir / 'evaluation_summary_report.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"汇总报告已保存: {report_file}")
    
    def generate_all_visualizations(self):
        """生成所有可视化图表"""
        logger.info("开始生成所有可视化图表...")
        
        if not self.results_data:
            logger.error("没有加载到评测结果数据")
            return
        
        try:
            self.create_performance_overview()
            self.create_metric_comparison()
            self.create_category_breakdown()
            self.generate_summary_report()
            
            logger.info("所有可视化图表生成完成！")
            logger.info(f"结果保存在: {self.result_dir}")
            
        except Exception as e:
            logger.error(f"生成可视化图表时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='评测结果可视化工具')
    parser.add_argument('--result_dir', type=str, default='./result',
                       help='评测结果目录')
    
    args = parser.parse_args()
    
    visualizer = ResultVisualizer(args.result_dir)
    visualizer.generate_all_visualizations()

if __name__ == "__main__":
    main()
