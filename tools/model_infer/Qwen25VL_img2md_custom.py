#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen-2.5-VL 模型推理脚本 - 专为 OmniDocBench md2md 测试定制
支持多种 Qwen-2.5-VL 模型规格：7B, 72B
"""

import os
import json
import torch
from tqdm import tqdm
from pathlib import Path
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from qwen_vl_utils import process_vision_info
import argparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QwenVLInference:
    def __init__(self, model_path, device_map="auto", torch_dtype=torch.bfloat16):
        """
        初始化 Qwen-2.5-VL 模型
        
        Args:
            model_path: 模型路径（本地路径或 HuggingFace 模型名）
            device_map: 设备映射策略
            torch_dtype: 数据类型
        """
        self.model_path = model_path
        self.device_map = device_map
        self.torch_dtype = torch_dtype
        
        logger.info(f"正在加载模型: {model_path}")
        self._load_model()
        
    def _load_model(self):
        """加载模型和处理器"""
        try:
            # 加载模型
            self.model = Qwen2VLForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=self.torch_dtype,
                device_map=self.device_map,
                attn_implementation="flash_attention_2"  # 使用 Flash Attention 2 加速
            )
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(self.model_path)
            
            logger.info("模型加载成功！")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def generate_markdown(self, image_path, prompt=None, max_new_tokens=32000, temperature=0.01):
        """
        从图片生成 Markdown 内容
        
        Args:
            image_path: 图片路径
            prompt: 自定义提示词
            max_new_tokens: 最大生成 token 数
            temperature: 生成温度
            
        Returns:
            生成的 Markdown 文本
        """
        if prompt is None:
            prompt = self._get_default_prompt()
        
        # 构建消息
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image": image_path,
                    },
                    {"type": "text", "text": prompt},
                ],
            }
        ]
        
        try:
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            image_inputs, video_inputs = process_vision_info(messages)
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            inputs = inputs.to("cuda")
            
            # 生成输出
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    do_sample=False
                )
            
            # 解码输出
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )
            
            return output_text[0]
            
        except Exception as e:
            logger.error(f"生成失败 {image_path}: {e}")
            return ""
    
    def _get_default_prompt(self):
        """获取默认的文档解析提示词"""
        return '''You are an AI assistant specialized in converting PDF images to Markdown format. Please follow these instructions for the conversion:

1. Text Processing:
   - Accurately recognize all text content in the PDF image without guessing or inferring.
   - Convert the recognized text into Markdown format.
   - Maintain the original document structure, including headings, paragraphs, lists, etc.

2. Mathematical Formula Processing:
   - Convert all mathematical formulas to LaTeX format.
   - Enclose inline formulas with \\( \\). For example: This is an inline formula \\( E = mc^2 \\)
   - Enclose block formulas with \\[ \\]. For example: \\[ \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a} \\]

3. Table Processing:
   - Convert tables to HTML format.
   - Wrap the entire table with <table> and </table>.

4. Figure Handling:
   - Ignore figures content in the PDF image. Do not attempt to describe or convert images.

5. Output Format:
   - Ensure the output Markdown document has a clear structure with appropriate line breaks between elements.
   - For complex layouts, try to maintain the original document's structure and format as closely as possible.

Please strictly follow these guidelines to ensure accuracy and consistency in the conversion. Your task is to accurately convert the content of the PDF image into Markdown format without adding any extra explanations or comments.'''

def process_images_batch(model, input_dir, output_dir, image_extensions=('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp')):
    """
    批量处理图片
    
    Args:
        model: QwenVLInference 实例
        input_dir: 输入图片目录
        output_dir: 输出 Markdown 目录
        image_extensions: 支持的图片格式
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 收集所有图片文件
    image_files = []
    for root, _, files in os.walk(input_dir):
        for name in files:
            if any(name.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(root, name))
    
    logger.info(f"找到 {len(image_files)} 个图片文件")
    
    # 批量处理
    for image_path in tqdm(image_files, desc="处理图片"):
        # 构建输出文件路径
        basename = os.path.splitext(os.path.basename(image_path))[0]
        markdown_file = os.path.join(output_dir, f"{basename}.md")
        
        # 跳过已存在的文件
        if os.path.exists(markdown_file):
            logger.info(f"文件已存在，跳过: {markdown_file}")
            continue
        
        # 生成 Markdown
        try:
            markdown_content = model.generate_markdown(image_path)
            
            # 保存结果
            with open(markdown_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"已保存: {markdown_file}")
            
        except Exception as e:
            logger.error(f"处理失败 {image_path}: {e}")

def main():
    parser = argparse.ArgumentParser(description='Qwen-2.5-VL 文档解析推理脚本')
    parser.add_argument('--model_path', type=str, required=True, 
                       help='模型路径（本地路径或 HuggingFace 模型名）')
    parser.add_argument('--input_dir', type=str, required=True,
                       help='输入图片目录')
    parser.add_argument('--output_dir', type=str, required=True,
                       help='输出 Markdown 目录')
    parser.add_argument('--max_new_tokens', type=int, default=32000,
                       help='最大生成 token 数')
    parser.add_argument('--temperature', type=float, default=0.01,
                       help='生成温度')
    
    args = parser.parse_args()
    
    # 初始化模型
    model = QwenVLInference(args.model_path)
    
    # 批量处理
    process_images_batch(model, args.input_dir, args.output_dir)
    
    logger.info("处理完成！")

if __name__ == "__main__":
    main()
