#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义数据集准备工具
支持从各种格式的数据创建 OmniDocBench 兼容的数据集
"""

import os
import json
import shutil
import argparse
from pathlib import Path
from PIL import Image
import logging
from tqdm import tqdm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatasetPreparer:
    def __init__(self, source_dir, target_dir):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
        
    def create_directory_structure(self):
        """创建标准目录结构"""
        directories = [
            self.target_dir / "images",
            self.target_dir / "ground_truth" / "mds",
            self.target_dir / "predictions"
        ]
        
        for dir_path in directories:
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {dir_path}")
    
    def validate_and_copy_images(self):
        """验证并复制图片文件"""
        logger.info("处理图片文件...")
        
        image_files = []
        for file_path in self.source_dir.rglob("*"):
            if file_path.suffix.lower() in self.supported_image_formats:
                image_files.append(file_path)
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        processed_files = []
        for img_path in tqdm(image_files, desc="处理图片"):
            try:
                # 验证图片可以正常打开
                with Image.open(img_path) as img:
                    width, height = img.size
                    if width < 800 or height < 600:
                        logger.warning(f"图片分辨率较低: {img_path} ({width}x{height})")
                
                # 生成标准化文件名
                base_name = self.generate_standard_name(img_path)
                target_path = self.target_dir / "images" / f"{base_name}{img_path.suffix.lower()}"
                
                # 复制文件
                shutil.copy2(img_path, target_path)
                processed_files.append({
                    'original_path': str(img_path),
                    'target_path': str(target_path),
                    'base_name': base_name,
                    'dimensions': f"{width}x{height}"
                })
                
            except Exception as e:
                logger.error(f"处理图片失败 {img_path}: {e}")
        
        logger.info(f"成功处理 {len(processed_files)} 个图片文件")
        return processed_files
    
    def generate_standard_name(self, file_path):
        """生成标准化文件名"""
        # 使用原文件名（去除扩展名）
        base_name = file_path.stem
        
        # 清理文件名：移除特殊字符，替换为下划线
        import re
        base_name = re.sub(r'[^\w\-_.]', '_', base_name)
        
        return base_name
    
    def create_markdown_templates(self, processed_files):
        """为每个图片创建 Markdown 模板"""
        logger.info("创建 Markdown 模板...")
        
        template = """# 文档标题

## 主要内容

这里是文档的主要文本内容。

### 公式示例
行内公式：\\( E = mc^2 \\)

行间公式：
\\[ \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a} \\]

### 表格示例
<table>
<tr>
<th>列1</th>
<th>列2</th>
</tr>
<tr>
<td>数据1</td>
<td>数据2</td>
</tr>
</table>

## 注意事项
请根据实际文档内容修改此模板。
"""
        
        for file_info in tqdm(processed_files, desc="创建模板"):
            md_path = self.target_dir / "ground_truth" / "mds" / f"{file_info['base_name']}.md"
            
            if not md_path.exists():
                with open(md_path, 'w', encoding='utf-8') as f:
                    f.write(template)
                logger.info(f"创建模板: {md_path}")
    
    def create_dataset_json(self, processed_files):
        """创建数据集 JSON 文件"""
        logger.info("创建数据集 JSON 文件...")
        
        dataset_info = []
        for file_info in processed_files:
            page_info = {
                "page_info": {
                    "image_path": f"images/{file_info['base_name']}{Path(file_info['target_path']).suffix}",
                    "page_attribute": {
                        "language": "english",  # 默认值，需要根据实际情况修改
                        "data_source": "custom",
                        "layout": "single_column",
                        "watermark": False,
                        "fuzzy_scan": False,
                        "colorful_background": False
                    }
                }
            }
            dataset_info.append(page_info)
        
        json_path = self.target_dir / "ground_truth" / "dataset.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, ensure_ascii=False, indent=2)
        
        logger.info(f"创建数据集 JSON: {json_path}")
    
    def create_readme(self):
        """创建说明文件"""
        readme_content = """# 自定义数据集

## 目录结构
- `images/`: 输入图片文件
- `ground_truth/mds/`: 标准答案 Markdown 文件
- `ground_truth/dataset.json`: 页面属性信息
- `predictions/`: 模型预测结果（自动生成）

## 使用步骤
1. 检查并修改 `ground_truth/mds/` 中的 Markdown 文件
2. 根据实际情况更新 `ground_truth/dataset.json` 中的页面属性
3. 运行评测脚本

## 注意事项
- 确保图片文件名与 Markdown 文件名对应
- 所有文件使用 UTF-8 编码
- Markdown 文件格式需符合 OmniDocBench 规范
"""
        
        readme_path = self.target_dir / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        logger.info(f"创建说明文件: {readme_path}")
    
    def prepare_dataset(self):
        """执行完整的数据集准备流程"""
        logger.info(f"开始准备数据集: {self.source_dir} -> {self.target_dir}")
        
        try:
            # 1. 创建目录结构
            self.create_directory_structure()
            
            # 2. 处理图片文件
            processed_files = self.validate_and_copy_images()
            
            if not processed_files:
                logger.error("没有找到有效的图片文件")
                return False
            
            # 3. 创建 Markdown 模板
            self.create_markdown_templates(processed_files)
            
            # 4. 创建数据集 JSON
            self.create_dataset_json(processed_files)
            
            # 5. 创建说明文件
            self.create_readme()
            
            logger.info("数据集准备完成！")
            logger.info(f"请检查并修改 {self.target_dir}/ground_truth/mds/ 中的 Markdown 文件")
            
            return True
            
        except Exception as e:
            logger.error(f"数据集准备失败: {e}")
            return False

def convert_pdf_to_images(pdf_dir, output_dir, dpi=300):
    """将 PDF 文件转换为图片"""
    try:
        from pdf2image import convert_from_path
    except ImportError:
        logger.error("请安装 pdf2image: pip install pdf2image")
        return
    
    logger.info(f"转换 PDF 文件: {pdf_dir} -> {output_dir}")
    
    pdf_files = list(Path(pdf_dir).glob("*.pdf"))
    logger.info(f"找到 {len(pdf_files)} 个 PDF 文件")
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    for pdf_file in tqdm(pdf_files, desc="转换 PDF"):
        try:
            pages = convert_from_path(pdf_file, dpi=dpi)
            
            for i, page in enumerate(pages):
                image_name = f"{pdf_file.stem}_page_{i+1:03d}.jpg"
                image_path = output_path / image_name
                page.save(image_path, 'JPEG', quality=95)
                
        except Exception as e:
            logger.error(f"转换失败 {pdf_file}: {e}")

def main():
    parser = argparse.ArgumentParser(description='自定义数据集准备工具')
    parser.add_argument('--source_dir', type=str, required=True,
                       help='源数据目录（包含图片或 PDF 文件）')
    parser.add_argument('--target_dir', type=str, required=True,
                       help='目标数据集目录')
    parser.add_argument('--convert_pdf', action='store_true',
                       help='是否将 PDF 转换为图片')
    parser.add_argument('--pdf_dpi', type=int, default=300,
                       help='PDF 转换 DPI')
    
    args = parser.parse_args()
    
    # 如果需要转换 PDF
    if args.convert_pdf:
        temp_dir = Path(args.target_dir) / "temp_images"
        convert_pdf_to_images(args.source_dir, temp_dir, args.pdf_dpi)
        source_dir = temp_dir
    else:
        source_dir = args.source_dir
    
    # 准备数据集
    preparer = DatasetPreparer(source_dir, args.target_dir)
    success = preparer.prepare_dataset()
    
    # 清理临时文件
    if args.convert_pdf and Path(args.target_dir, "temp_images").exists():
        shutil.rmtree(Path(args.target_dir) / "temp_images")
    
    if success:
        logger.info("数据集准备成功！")
    else:
        logger.error("数据集准备失败！")

if __name__ == "__main__":
    main()
