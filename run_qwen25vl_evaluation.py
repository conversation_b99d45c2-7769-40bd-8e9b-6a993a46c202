#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen-2.5-VL 完整评测流程脚本
包含数据准备、模型推理、评测执行的完整流程
"""

import os
import sys
import argparse
import subprocess
import logging
from pathlib import Path
import json
import shutil

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QwenEvaluationPipeline:
    def __init__(self, config):
        self.config = config
        self.project_root = Path(__file__).parent
        
    def setup_directories(self):
        """创建必要的目录结构"""
        logger.info("创建目录结构...")
        
        directories = [
            self.config['output_dir'],
            self.config['result_dir'],
            'logs'
        ]
        
        for dir_path in directories:
            os.makedirs(dir_path, exist_ok=True)
            logger.info(f"创建目录: {dir_path}")
    
    def validate_data(self):
        """验证数据完整性"""
        logger.info("验证数据完整性...")
        
        # 检查输入图片目录
        if not os.path.exists(self.config['input_images']):
            raise FileNotFoundError(f"输入图片目录不存在: {self.config['input_images']}")
        
        # 检查标准答案目录
        if not os.path.exists(self.config['ground_truth_mds']):
            raise FileNotFoundError(f"标准答案目录不存在: {self.config['ground_truth_mds']}")
        
        # 检查 JSON 文件
        if not os.path.exists(self.config['ground_truth_json']):
            raise FileNotFoundError(f"标准答案 JSON 文件不存在: {self.config['ground_truth_json']}")
        
        # 统计文件数量
        image_files = [f for f in os.listdir(self.config['input_images']) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'))]
        md_files = [f for f in os.listdir(self.config['ground_truth_mds']) 
                   if f.endswith('.md')]
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        logger.info(f"找到 {len(md_files)} 个标准答案文件")
        
        return len(image_files), len(md_files)
    
    def run_inference(self):
        """执行模型推理"""
        logger.info("开始执行 Qwen-2.5-VL 模型推理...")
        
        inference_script = self.project_root / "tools/model_infer/Qwen25VL_img2md_custom.py"
        
        cmd = [
            "python", str(inference_script),
            "--model_path", self.config['model_path'],
            "--input_dir", self.config['input_images'],
            "--output_dir", self.config['output_dir'],
            "--max_new_tokens", str(self.config.get('max_new_tokens', 32000)),
            "--temperature", str(self.config.get('temperature', 0.01))
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("模型推理完成")
            logger.info(result.stdout)
        except subprocess.CalledProcessError as e:
            logger.error(f"模型推理失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            raise
    
    def run_evaluation(self):
        """执行评测"""
        logger.info("开始执行 md2md 评测...")
        
        # 更新配置文件中的路径
        self.update_config_file()
        
        # 执行评测
        cmd = [
            "python", "pdf_validation.py",
            "--config", "configs/md2md.yaml"
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, cwd=self.project_root)
            logger.info("评测完成")
            logger.info(result.stdout)
        except subprocess.CalledProcessError as e:
            logger.error(f"评测失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            raise
    
    def update_config_file(self):
        """更新配置文件中的路径"""
        config_file = self.project_root / "configs/md2md.yaml"
        
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新路径
        content = content.replace(
            "data_path: ./result/qwen25vl_predictions",
            f"data_path: {self.config['output_dir']}"
        )
        content = content.replace(
            "data_path: ./demo_data/omnidocbench_demo/mds",
            f"data_path: {self.config['ground_truth_mds']}"
        )
        content = content.replace(
            "page_info: ./demo_data/omnidocbench_demo/OmniDocBench_demo.json",
            f"page_info: {self.config['ground_truth_json']}"
        )
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("配置文件已更新")
    
    def analyze_results(self):
        """分析评测结果"""
        logger.info("分析评测结果...")
        
        result_files = list(Path("result").glob("*metric_result.json"))
        
        if not result_files:
            logger.warning("未找到评测结果文件")
            return
        
        for result_file in result_files:
            logger.info(f"分析结果文件: {result_file}")
            
            with open(result_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 打印主要指标
            for category, metrics in results.items():
                logger.info(f"\n=== {category} ===")
                for metric_name, values in metrics.items():
                    if isinstance(values, dict):
                        for group, score in values.items():
                            logger.info(f"{metric_name} ({group}): {score:.4f}")
                    else:
                        logger.info(f"{metric_name}: {values}")
    
    def run_full_pipeline(self):
        """执行完整的评测流程"""
        logger.info("开始执行完整的 Qwen-2.5-VL 评测流程")
        
        try:
            # 1. 设置目录
            self.setup_directories()
            
            # 2. 验证数据
            img_count, md_count = self.validate_data()
            
            # 3. 执行推理
            if not self.config.get('skip_inference', False):
                self.run_inference()
            else:
                logger.info("跳过模型推理步骤")
            
            # 4. 执行评测
            self.run_evaluation()
            
            # 5. 分析结果
            self.analyze_results()
            
            logger.info("评测流程完成！")
            
        except Exception as e:
            logger.error(f"评测流程失败: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='Qwen-2.5-VL 完整评测流程')
    parser.add_argument('--model_path', type=str, required=True,
                       help='Qwen-2.5-VL 模型路径')
    parser.add_argument('--input_images', type=str, required=True,
                       help='输入图片目录')
    parser.add_argument('--ground_truth_mds', type=str, required=True,
                       help='标准答案 markdown 目录')
    parser.add_argument('--ground_truth_json', type=str, required=True,
                       help='标准答案 JSON 文件')
    parser.add_argument('--output_dir', type=str, default='./result/qwen25vl_predictions',
                       help='模型预测输出目录')
    parser.add_argument('--result_dir', type=str, default='./result',
                       help='评测结果目录')
    parser.add_argument('--skip_inference', action='store_true',
                       help='跳过模型推理步骤')
    parser.add_argument('--max_new_tokens', type=int, default=32000,
                       help='最大生成 token 数')
    parser.add_argument('--temperature', type=float, default=0.01,
                       help='生成温度')
    
    args = parser.parse_args()
    
    # 构建配置
    config = {
        'model_path': args.model_path,
        'input_images': args.input_images,
        'ground_truth_mds': args.ground_truth_mds,
        'ground_truth_json': args.ground_truth_json,
        'output_dir': args.output_dir,
        'result_dir': args.result_dir,
        'skip_inference': args.skip_inference,
        'max_new_tokens': args.max_new_tokens,
        'temperature': args.temperature
    }
    
    # 执行评测
    pipeline = QwenEvaluationPipeline(config)
    pipeline.run_full_pipeline()

if __name__ == "__main__":
    main()
