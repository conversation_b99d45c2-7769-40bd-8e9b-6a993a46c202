# OmniDocBench 评测指标详细解释

## 1. 编辑距离 (Edit Distance)

### 定义
标准化编辑距离是衡量两个字符串相似度的重要指标，计算将一个字符串转换为另一个字符串所需的最少编辑操作次数。

### 计算公式
```
Normalized Edit Distance = Levenshtein Distance / max(len(GT), len(Pred))
```

### 取值范围
- **0.0**: 完全匹配，最佳结果
- **1.0**: 完全不匹配，最差结果
- **越小越好**

### 应用场景
- 文本段落准确性评估
- 公式识别准确性评估
- 表格内容准确性评估
- 阅读顺序正确性评估

### 示例
```python
GT:   "The quick brown fox"
Pred: "The quik brown fox"
Levenshtein Distance: 1 (删除一个 'c')
Normalized Edit Distance: 1 / 19 = 0.053
```

## 2. BLEU 分数

### 定义
BLEU (Bilingual Evaluation Understudy) 是机器翻译领域的经典评测指标，基于 n-gram 精确度计算。

### 计算原理
1. 计算 1-gram, 2-gram, 3-gram, 4-gram 的精确度
2. 应用几何平均
3. 加入简洁性惩罚 (Brevity Penalty)

### 取值范围
- **0.0 - 1.0**: 分数越高越好
- **1.0**: 完美匹配

### 优势与局限
- **优势**: 考虑词序，对流畅性敏感
- **局限**: 不考虑语义相似性

### 适用场景
- 文本段落质量评估
- 长文本生成质量评估

## 3. METEOR 分数

### 定义
METEOR (Metric for Evaluation of Translation with Explicit ORdering) 是改进的文本评测指标。

### 核心特性
1. **词干匹配**: 识别词汇的不同形态
2. **同义词匹配**: 考虑语义相似性
3. **词序惩罚**: 对词序错误进行惩罚

### 计算步骤
1. 计算精确度 (Precision) 和召回率 (Recall)
2. 计算 F-measure
3. 应用词序惩罚

### 取值范围
- **0.0 - 1.0**: 分数越高越好

### 相比 BLEU 的优势
- 更好的语义理解
- 对同义词替换更宽容
- 更符合人类判断

## 4. TEDS 分数 (表格专用)

### 定义
TEDS (Tree Edit Distance based Similarity) 专门用于评测表格结构和内容的相似度。

### 核心思想
1. 将表格转换为树结构
2. 计算树编辑距离
3. 标准化得到相似度分数

### 评测维度
- **结构相似度**: 表格行列结构
- **内容相似度**: 单元格内容匹配
- **合并单元格**: 复杂表格结构处理

### 计算公式
```
TEDS = 1 - (Tree Edit Distance / max(|T_GT|, |T_Pred|))
```

### 取值范围
- **0.0 - 1.0**: 分数越高越好
- **1.0**: 表格完全匹配

### 变体
- **TEDS**: 结构 + 内容
- **TEDS-Struct**: 仅结构相似度

## 5. CDM 格式 (公式专用)

### 定义
CDM (Character Detection Metric) 是专门用于数学公式识别评测的格式。

### 特点
- 字符级别的精确匹配
- 支持复杂数学符号
- 与 UniMERNet 评测标准兼容

### 输出格式
生成符合 CDM 评测要求的格式文件，可直接用于公式识别模型的标准评测。

## 6. 分组评测结果解读

### 页面属性分组
评测结果会按照页面属性进行分组统计：

#### 语言类型
- **english**: 英文页面
- **simplified_chinese**: 简体中文页面
- **en_ch_mixed**: 中英混合页面

#### 文档类型
- **academic_literature**: 学术文献
- **book**: 图书教材
- **magazine**: 杂志
- **newspaper**: 报纸
- **exam_paper**: 试卷
- **research_report**: 研报财报

#### 布局类型
- **single_column**: 单栏布局
- **double_column**: 双栏布局
- **three_column**: 三栏布局

### 结果文件结构
```json
{
  "text_block": {
    "Edit_dist": {
      "{'language': 'english'}": 0.123,
      "{'language': 'simplified_chinese'}": 0.156,
      "overall": 0.139
    },
    "BLEU": {
      "{'language': 'english'}": 0.876,
      "overall": 0.845
    }
  },
  "display_formula": {
    "Edit_dist": {
      "overall": 0.234
    }
  }
}
```

## 7. 性能基准参考

### 优秀性能指标 (参考值)
- **文本 Edit Distance**: < 0.15
- **文本 BLEU**: > 0.80
- **文本 METEOR**: > 0.75
- **公式 Edit Distance**: < 0.30
- **表格 TEDS**: > 0.75
- **阅读顺序 Edit Distance**: < 0.20

### 性能等级划分
#### 文本识别
- **优秀**: Edit Distance < 0.10
- **良好**: 0.10 ≤ Edit Distance < 0.20
- **一般**: 0.20 ≤ Edit Distance < 0.30
- **较差**: Edit Distance ≥ 0.30

#### 公式识别
- **优秀**: Edit Distance < 0.20
- **良好**: 0.20 ≤ Edit Distance < 0.35
- **一般**: 0.35 ≤ Edit Distance < 0.50
- **较差**: Edit Distance ≥ 0.50

#### 表格识别
- **优秀**: TEDS > 0.80
- **良好**: 0.65 < TEDS ≤ 0.80
- **一般**: 0.50 < TEDS ≤ 0.65
- **较差**: TEDS ≤ 0.50

## 8. 结果分析建议

### 诊断性分析
1. **查看分类别结果**: 识别模型在哪些类型内容上表现较差
2. **对比不同指标**: BLEU 和 METEOR 的差异可反映语义理解能力
3. **分析页面属性**: 不同文档类型、语言的性能差异

### 改进方向
1. **文本识别差**: 考虑 OCR 模型优化
2. **公式识别差**: 加强数学符号训练
3. **表格识别差**: 改进表格结构理解
4. **阅读顺序差**: 优化布局分析算法

### 对比分析
- 与论文中的基准模型对比
- 不同模型规格间的性能权衡
- 推理参数对结果的影响
