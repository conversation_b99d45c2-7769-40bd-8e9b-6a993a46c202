#!/bin/bash

# Qwen-2.5-VL 环境配置脚本
echo "开始配置 Qwen-2.5-VL 测试环境..."

# 1. 创建虚拟环境
conda create -n qwen_omnidocbench python=3.10 -y
conda activate qwen_omnidocbench

# 2. 安装基础依赖
pip install -r requirements.txt

# 3. 安装 Qwen-VL 相关依赖
pip install transformers>=4.37.0
pip install torch>=2.0.0
pip install torchvision
pip install qwen-vl-utils
pip install modelscope
pip install accelerate
pip install flash-attn --no-build-isolation

# 4. 安装评测相关依赖
pip install evaluate
pip install python-Levenshtein
pip install scipy
pip install tqdm
pip install Pillow

# 5. 可选：安装 LaTeXML（如果需要评测 LaTeX 表格）
# sudo apt-get install latexml  # Ubuntu/Debian
# brew install latexml          # macOS

echo "环境配置完成！"
echo "请使用以下命令激活环境："
echo "conda activate qwen_omnidocbench"
