aiohappyeyeballs==2.4.3
aiohttp==3.10.11
aiosignal==1.3.1
apted==1.0.3
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
async-timeout==5.0.1
attrs==24.2.0
backcall==0.2.0
beautifulsoup4==4.11.1
bleach==5.0.1
category-encoders==2.5.0
certifi==2024.8.30
charset-normalizer==3.4.0
click==8.1.7
contourpy==1.1.1
cycler==0.12.1
datasets==3.1.0
debugpy==1.6.3
decorator==5.1.1
defusedxml==0.7.1
dill==0.3.8
entrypoints==0.4
evaluate==0.4.3
fastjsonschema==2.16.2
filelock==3.16.1
fonttools==4.55.0
frozenlist==1.5.0
fsspec==2024.9.0
func-timeout==4.3.5
huggingface-hub==0.26.2
idna==3.10
importlib_resources==6.4.5
ipykernel==6.16.0
ipython==7.34.0
ipython-genutils==0.2.0
ipywidgets==7.7.2
jedi==0.18.1
Jinja2==3.1.2
joblib==1.2.0
jsonpickle==2.2.0
jupyterlab-pygments==0.2.2
jupyterlab-widgets==1.1.1
kiwisolver==1.4.7
Levenshtein==0.25.1
loguru==0.7.2
lxml==4.9.1
markdown-it-py==3.0.0
matplotlib==3.7.5
matplotlib-inline==0.1.6
mdurl==0.1.2
mistune==2.0.4
mmeval==0.2.1
multidict==6.1.0
multiprocess==0.70.16
nbclient==0.6.8
nbconvert==7.0.0
nbformat==5.6.1
nest-asyncio==1.5.5
nltk==3.9.1
notebook==6.4.12
numpy==1.24.4
opencv-python==*********
packaging==24.2
pandas==2.0.3
pandocfilters==1.5.0
parso==0.8.3
patsy==0.5.2
pexpect==4.8.0
pickleshare==0.7.5
pillow==10.4.0
pkgutil_resolve_name==1.3.10
plotly==5.5.0
plum-dispatch==1.7.4
prometheus-client==0.14.1
prompt-toolkit==3.0.31
propcache==0.2.0
psutil==5.9.2
ptyprocess==0.7.0
pyarrow==17.0.0
Pygments==2.18.0
pylatexenc==3.0a30
PyNomaly==0.3.3
pyparsing==3.1.4
pyrsistent==0.18.1
python-dateutil==2.9.0.post0
python-utils==3.3.3
pytz==2024.2
PyYAML==6.0.2
rapidfuzz==3.9.7
regex==2024.11.6
requests==2.32.3
rich==13.9.4
scikit-learn==1.1.2
scipy==1.10.1
Send2Trash==1.8.0
six==1.16.0
soupsieve==2.3.2.post1
statsmodels==0.13.2
tabulate==0.9.0
tenacity==8.1.0
terminado==0.15.0
threadpoolctl==3.1.0
tinycss2==1.1.1
tornado==6.2
tqdm==4.67.1
typing_extensions==4.12.2
tzdata==2024.2
urllib3==2.2.3
webencodings==0.5.1
widgetsnbextension==3.6.1
xxhash==3.5.0
yarl==1.15.2
zipp==3.20.2
pycocotools==2.0.7