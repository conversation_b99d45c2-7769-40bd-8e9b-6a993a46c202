# OmniDocBench 数据格式详细指南

## 1. 支持的输入文件格式

### 图片格式（模型输入）
- **主要格式**: `.jpg`, `.jpeg`, `.png`
- **其他支持**: `.gif`, `.bmp`, `.tiff`, `.webp`
- **推荐格式**: `.jpg` (平衡质量和文件大小)
- **分辨率要求**: 建议 1200x1600 像素以上，确保文字清晰可读

### PDF 格式（可选）
- 支持直接从 PDF 提取页面图片
- 建议 DPI: 300 以上
- 可使用 `pdf2image` 库进行转换

## 2. 目录结构要求

### 标准目录结构
```
your_dataset/
├── images/                    # 输入图片目录
│   ├── document_001.jpg
│   ├── document_002.jpg
│   └── ...
├── ground_truth/
│   ├── mds/                   # 标准答案 Markdown 文件
│   │   ├── document_001.md
│   │   ├── document_002.md
│   │   └── ...
│   └── dataset.json           # 页面属性信息（可选）
└── predictions/               # 模型预测结果（自动生成）
    ├── document_001.md
    ├── document_002.md
    └── ...
```

### 文件命名规则
- **图片文件**: `{basename}.{ext}` (如: `document_001.jpg`)
- **Markdown 文件**: `{basename}.md` (如: `document_001.md`)
- **文件名必须一一对应**，仅扩展名不同

## 3. Markdown 标准答案格式

### 基本结构
```markdown
# 标题示例

这是一个段落文本。

## 二级标题

这是另一个段落，包含行内公式 \( E = mc^2 \)。

行间公式示例：
\[ \frac{-b \pm \sqrt{b^2 - 4ac}}{2a} \]

### 表格示例
<table>
<tr>
<th>列1</th>
<th>列2</th>
</tr>
<tr>
<td>数据1</td>
<td>数据2</td>
</tr>
</table>
```

### 公式格式规范
- **行内公式**: `\( formula \)` 或 `$formula$`
- **行间公式**: `\[ formula \]` 或 `$$formula$$`
- **LaTeX 语法**: 支持完整的 LaTeX 数学符号

### 表格格式规范
- **HTML 格式**: `<table>...</table>` (推荐)
- **Markdown 格式**: `| col1 | col2 |` (简单表格)
- **LaTeX 格式**: `\begin{table}...\end{table}` (复杂表格)

## 4. 页面属性信息（可选）

### JSON 格式示例
```json
[
  {
    "page_info": {
      "image_path": "document_001.jpg",
      "page_attribute": {
        "language": "english",
        "data_source": "academic_literature",
        "layout": "double_column",
        "watermark": false,
        "fuzzy_scan": false
      }
    }
  }
]
```

### 支持的属性标签
- **语言类型**: `english`, `simplified_chinese`, `en_ch_mixed`
- **文档类型**: `academic_literature`, `book`, `magazine`, `newspaper`, `exam_paper`, `note`, `research_report`, `PPT2PDF`, `colorful_textbook`
- **布局类型**: `single_column`, `double_column`, `three_column`, `1andmore_column`, `other_layout`
- **特殊属性**: `watermark`, `fuzzy_scan`, `colorful_background`

## 5. 数据质量要求

### 图片质量
- **清晰度**: 文字必须清晰可读
- **完整性**: 页面内容完整，无裁剪
- **方向**: 正确的阅读方向
- **对比度**: 文字与背景对比度足够

### 标注质量
- **准确性**: 文字识别准确率 > 95%
- **完整性**: 包含所有文本、公式、表格内容
- **格式**: 严格按照 Markdown 格式规范
- **结构**: 保持原文档的逻辑结构

## 6. 常见问题与解决方案

### 文件名不匹配
- **问题**: 图片文件名与 Markdown 文件名不对应
- **解决**: 使用批量重命名工具统一文件名

### 编码问题
- **问题**: 中文字符显示乱码
- **解决**: 确保所有文件使用 UTF-8 编码

### 公式格式错误
- **问题**: LaTeX 公式无法正确解析
- **解决**: 检查公式语法，使用标准 LaTeX 格式

### 表格格式不统一
- **问题**: 混用不同的表格格式
- **解决**: 统一使用 HTML 表格格式
